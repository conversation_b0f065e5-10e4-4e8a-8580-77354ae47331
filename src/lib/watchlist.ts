import { createClient } from './supabase'
import { Title, TitleAction, List, ListItem } from './supabase'

export class WatchlistService {
  private supabase = createClient()

  // Get or create a title in the database
  async getOrCreateTitle(tmdbData: {
    id: number
    media_type: 'movie' | 'tv'
    title?: string
    name?: string
    release_date?: string
    first_air_date?: string
    poster_path?: string
    backdrop_path?: string
    overview?: string
  }): Promise<Title | null> {
    const title = tmdbData.title || tmdbData.name || 'Unknown Title'
    const year = tmdbData.release_date || tmdbData.first_air_date
      ? new Date(tmdbData.release_date || tmdbData.first_air_date!).getFullYear()
      : null

    // First, try to find existing title
    const { data: existingTitle } = await this.supabase
      .from('titles')
      .select('*')
      .eq('source', 'tmdb')
      .eq('source_id', tmdbData.id.toString())
      .eq('content_type', tmdbData.media_type)
      .single()

    if (existingTitle) {
      return existingTitle
    }

    // Create new title
    const { data: newTitle, error } = await this.supabase
      .from('titles')
      .insert({
        content_type: tmdbData.media_type,
        source: 'tmdb',
        source_id: tmdbData.id.toString(),
        title,
        year,
        poster_url: tmdbData.poster_path 
          ? `https://image.tmdb.org/t/p/w500${tmdbData.poster_path}`
          : null,
        backdrop_url: tmdbData.backdrop_path
          ? `https://image.tmdb.org/t/p/w1280${tmdbData.backdrop_path}`
          : null,
        overview: tmdbData.overview || null,
        metadata: tmdbData,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating title:', error)
      return null
    }

    return newTitle
  }

  // Add to watchlist
  async addToWatchlist(titleId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('title_actions')
        .upsert({
          user_id: userId,
          title_id: titleId,
          is_watchlisted: true,
        })

      return !error
    } catch (error) {
      console.error('Error adding to watchlist:', error)
      return false
    }
  }

  // Remove from watchlist
  async removeFromWatchlist(titleId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('title_actions')
        .upsert({
          user_id: userId,
          title_id: titleId,
          is_watchlisted: false,
        })

      return !error
    } catch (error) {
      console.error('Error removing from watchlist:', error)
      return false
    }
  }

  // Toggle like
  async toggleLike(titleId: string, userId: string): Promise<boolean> {
    try {
      // Get current state
      const { data: currentAction } = await this.supabase
        .from('title_actions')
        .select('liked')
        .eq('user_id', userId)
        .eq('title_id', titleId)
        .single()

      const newLikedState = !currentAction?.liked

      const { error } = await this.supabase
        .from('title_actions')
        .upsert({
          user_id: userId,
          title_id: titleId,
          liked: newLikedState,
        })

      return !error
    } catch (error) {
      console.error('Error toggling like:', error)
      return false
    }
  }

  // Toggle recommendation
  async toggleRecommendation(titleId: string, userId: string): Promise<boolean> {
    try {
      // Get current state
      const { data: currentAction } = await this.supabase
        .from('title_actions')
        .select('is_recommended')
        .eq('user_id', userId)
        .eq('title_id', titleId)
        .single()

      const newRecommendedState = !currentAction?.is_recommended

      const { error } = await this.supabase
        .from('title_actions')
        .upsert({
          user_id: userId,
          title_id: titleId,
          is_recommended: newRecommendedState,
        })

      return !error
    } catch (error) {
      console.error('Error toggling recommendation:', error)
      return false
    }
  }

  // Rate title
  async rateTitle(titleId: string, userId: string, rating: number): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('title_actions')
        .upsert({
          user_id: userId,
          title_id: titleId,
          rating,
        })

      return !error
    } catch (error) {
      console.error('Error rating title:', error)
      return false
    }
  }

  // Get user's title action
  async getTitleAction(titleId: string, userId: string): Promise<TitleAction | null> {
    try {
      const { data, error } = await this.supabase
        .from('title_actions')
        .select('*')
        .eq('user_id', userId)
        .eq('title_id', titleId)
        .single()

      if (error) return null
      return data
    } catch (error) {
      return null
    }
  }

  // Get user's watchlist
  async getWatchlist(userId: string): Promise<(Title & { title_actions: TitleAction })[]> {
    try {
      const { data, error } = await this.supabase
        .from('title_actions')
        .select(`
          *,
          titles (*)
        `)
        .eq('user_id', userId)
        .eq('is_watchlisted', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching watchlist:', error)
        return []
      }

      return data.map(item => ({
        ...item.titles,
        title_actions: item
      })) as (Title & { title_actions: TitleAction })[]
    } catch (error) {
      console.error('Error fetching watchlist:', error)
      return []
    }
  }

  // Get user's liked titles
  async getLikedTitles(userId: string): Promise<(Title & { title_actions: TitleAction })[]> {
    try {
      const { data, error } = await this.supabase
        .from('title_actions')
        .select(`
          *,
          titles (*)
        `)
        .eq('user_id', userId)
        .eq('liked', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching liked titles:', error)
        return []
      }

      return data.map(item => ({
        ...item.titles,
        title_actions: item
      })) as (Title & { title_actions: TitleAction })[]
    } catch (error) {
      console.error('Error fetching liked titles:', error)
      return []
    }
  }

  // Get user's recommended titles
  async getRecommendedTitles(userId: string): Promise<(Title & { title_actions: TitleAction })[]> {
    try {
      const { data, error } = await this.supabase
        .from('title_actions')
        .select(`
          *,
          titles (*)
        `)
        .eq('user_id', userId)
        .eq('is_recommended', true)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching recommended titles:', error)
        return []
      }

      return data.map(item => ({
        ...item.titles,
        title_actions: item
      })) as (Title & { title_actions: TitleAction })[]
    } catch (error) {
      console.error('Error fetching recommended titles:', error)
      return []
    }
  }

  // Get recommendations from followed users
  async getFollowedUsersRecommendations(userId: string): Promise<(Title & { title_actions: TitleAction & { profiles: any } })[]> {
    try {
      // First get the list of users that the current user follows
      const { data: followedUsers, error: followsError } = await this.supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', userId)

      if (followsError || !followedUsers || followedUsers.length === 0) {
        return []
      }

      const followedUserIds = followedUsers.map(f => f.following_id)

      // Then get recommendations from those users
      const { data, error } = await this.supabase
        .from('title_actions')
        .select(`
          *,
          titles (*),
          profiles!title_actions_user_id_fkey (
            user_id,
            username,
            display_name,
            avatar_url
          )
        `)
        .eq('is_recommended', true)
        .in('user_id', followedUserIds)
        .order('created_at', { ascending: false })
        .limit(50) // Limit to recent recommendations

      if (error) {
        console.error('Error fetching followed users recommendations:', error)
        return []
      }

      return data.map(item => ({
        ...item.titles,
        title_actions: {
          ...item,
          profiles: item.profiles
        }
      })) as (Title & { title_actions: TitleAction & { profiles: any } })[]
    } catch (error) {
      console.error('Error fetching followed users recommendations:', error)
      return []
    }
  }

  // Get user's default watchlist
  async getDefaultWatchlist(userId: string): Promise<List | null> {
    try {
      const { data, error } = await this.supabase
        .from('lists')
        .select('*')
        .eq('user_id', userId)
        .eq('is_default', true)
        .single()

      if (error) return null
      return data
    } catch (error) {
      return null
    }
  }

  // === LIST MANAGEMENT FUNCTIONS ===

  // Get all user's lists
  async getUserLists(userId: string): Promise<List[]> {
    try {
      const { data, error } = await this.supabase
        .from('lists')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching user lists:', error)
        return []
      }

      return data
    } catch (error) {
      console.error('Error fetching user lists:', error)
      return []
    }
  }

  // Create a new list
  async createList(userId: string, name: string, description?: string, isPublic: boolean = false): Promise<List | null> {
    try {
      const { data, error } = await this.supabase
        .from('lists')
        .insert({
          user_id: userId,
          name,
          description: description || null,
          is_public: isPublic,
          is_default: false,
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating list:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error creating list:', error)
      return null
    }
  }

  // Update a list
  async updateList(listId: string, updates: { name?: string; description?: string; is_public?: boolean }): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('lists')
        .update(updates)
        .eq('id', listId)

      return !error
    } catch (error) {
      console.error('Error updating list:', error)
      return false
    }
  }

  // Delete a list
  async deleteList(listId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('lists')
        .delete()
        .eq('id', listId)

      return !error
    } catch (error) {
      console.error('Error deleting list:', error)
      return false
    }
  }

  // Add title to a specific list
  async addToList(listId: string, titleId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('list_items')
        .insert({
          list_id: listId,
          title_id: titleId,
          position: 0, // Add to top
        })

      return !error
    } catch (error) {
      console.error('Error adding to list:', error)
      return false
    }
  }

  // Remove title from a specific list
  async removeFromList(listId: string, titleId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('list_items')
        .delete()
        .eq('list_id', listId)
        .eq('title_id', titleId)

      return !error
    } catch (error) {
      console.error('Error removing from list:', error)
      return false
    }
  }

  // Get titles in a specific list
  async getListItems(listId: string): Promise<(Title & { list_item: ListItem })[]> {
    try {
      const { data, error } = await this.supabase
        .from('list_items')
        .select(`
          *,
          titles (*)
        `)
        .eq('list_id', listId)
        .order('position', { ascending: true })

      if (error) {
        console.error('Error fetching list items:', error)
        return []
      }

      return data.map(item => ({
        ...item.titles,
        list_item: item
      })) as (Title & { list_item: ListItem })[]
    } catch (error) {
      console.error('Error fetching list items:', error)
      return []
    }
  }

  // Get lists that contain a specific title
  async getListsContainingTitle(titleId: string, userId: string): Promise<List[]> {
    try {
      const { data, error } = await this.supabase
        .from('list_items')
        .select(`
          lists (*)
        `)
        .eq('title_id', titleId)
        .eq('lists.user_id', userId)

      if (error) {
        console.error('Error fetching lists containing title:', error)
        return []
      }

      return data.map(item => item.lists).filter(Boolean) as List[]
    } catch (error) {
      console.error('Error fetching lists containing title:', error)
      return []
    }
  }
}

export const watchlistService = new WatchlistService()
