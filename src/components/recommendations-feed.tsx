'use client'

import { useQuery } from '@tanstack/react-query'
import { useAuth } from '@/lib/auth-context'
import { watchlistService } from '@/lib/watchlist'
import { TitleActions } from './title-actions'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Users } from 'lucide-react'

interface RecommendationsFeedProps {
  className?: string
}

export function RecommendationsFeed({ className }: RecommendationsFeedProps) {
  const { user } = useAuth()

  // Get recommendations from followed users
  const { data: recommendations = [], isLoading, error } = useQuery({
    queryKey: ['followed-recommendations', user?.id],
    queryFn: () => user ? watchlistService.getFollowedUsersRecommendations(user.id) : [],
    enabled: !!user,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })

  if (!user) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-lg font-semibold mb-2">Sign in to see recommendations</h3>
        <p className="text-muted-foreground">
          Follow other users to see their movie and TV recommendations here.
        </p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className={className}>
        <div className="space-y-6">
          {[1, 2, 3, 4, 5].map((i) => (
            <Card key={i} className="p-4">
              <div className="flex items-start space-x-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                  <div className="flex space-x-4 mt-4">
                    <Skeleton className="h-32 w-24 rounded" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-3 w-32" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">⚠️</div>
        <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
        <p className="text-muted-foreground">
          We couldn't load recommendations right now. Please try again later.
        </p>
      </div>
    )
  }

  if (recommendations.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-lg font-semibold mb-2">No recommendations yet</h3>
        <p className="text-muted-foreground mb-4">
          Follow other users to see their movie and TV recommendations here.
        </p>
        <p className="text-sm text-muted-foreground">
          You can also start recommending titles yourself to share with your followers!
        </p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {recommendations.map((item) => {
          const title = item.title
          const year = item.year
          const posterUrl = item.poster_url || 'https://via.placeholder.com/500x750/374151/f3f4f6?text=No+Image'
          const contentType = item.content_type
          const recommender = item.title_actions.profiles
          const recommendedAt = item.title_actions.created_at

          // Create TMDB data object for TitleCard
          const tmdbData = {
            id: parseInt(item.source_id),
            media_type: item.content_type as 'movie' | 'tv',
            title: item.content_type === 'movie' ? item.title : undefined,
            name: item.content_type === 'tv' ? item.title : undefined,
            poster_path: item.metadata?.poster_path,
            backdrop_path: item.metadata?.backdrop_path,
            overview: item.overview,
            release_date: item.metadata?.release_date,
            first_air_date: item.metadata?.first_air_date,
          }

          return (
            <Card key={`${item.id}-${item.title_actions.id}`} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  {/* Recommender Info */}
                  <div className="flex items-center space-x-3 mb-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={recommender?.avatar_url} />
                      <AvatarFallback>
                        {(recommender?.display_name || recommender?.username || 'U')[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-sm">
                          {recommender?.display_name || recommender?.username || 'Unknown User'}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          recommended
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {new Date(recommendedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Title Content */}
                <div className="flex space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-24 h-36 relative overflow-hidden rounded-lg">
                      <img
                        src={posterUrl}
                        alt={title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = 'https://via.placeholder.com/500x750/374151/f3f4f6?text=No+Image'
                        }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="space-y-2">
                      <div>
                        <h3 className="font-semibold text-lg line-clamp-2">{title}</h3>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <span>{year}</span>
                          <span>•</span>
                          <Badge variant="outline" className="text-xs">
                            {contentType.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                      
                      {item.overview && (
                        <p className="text-sm text-muted-foreground line-clamp-3">
                          {item.overview}
                        </p>
                      )}

                      {/* Action Buttons */}
                      <div className="pt-2">
                        <TitleActions tmdbData={tmdbData} size="sm" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
