'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star } from 'lucide-react'
import { TMDbMovie, TMDbTVShow, tmdbClient } from '@/lib/tmdb'
import Image from 'next/image'
import Link from 'next/link'

interface SimilarContentProps {
  content: (TMDbMovie | TMDbTVShow)[]
  type: 'movie' | 'tv'
  isLoading?: boolean
}

export function SimilarContent({ content, type, isLoading }: SimilarContentProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">More Like This</h3>
        <div className="flex gap-4 overflow-x-auto pb-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="flex-shrink-0 w-32 space-y-2 animate-pulse">
              <div className="aspect-[2/3] bg-muted rounded-lg" />
              <div className="h-4 bg-muted rounded w-full" />
              <div className="h-3 bg-muted rounded w-2/3" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (content.length === 0) {
    return null
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">More Like This</h3>
      <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
        {content.slice(0, 12).map((item) => {
          const title = 'title' in item ? item.title : item.name
          const releaseDate = 'release_date' in item ? item.release_date : item.first_air_date
          const year = releaseDate ? new Date(releaseDate).getFullYear() : 'TBA'
          const detailsUrl = `/details/${type}/${item.id}`

          return (
            <Link key={`${type}-${item.id}`} href={detailsUrl}>
              <Card className="flex-shrink-0 w-32 overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
                <div className="aspect-[2/3] relative overflow-hidden">
                  <Image
                    src={tmdbClient.getImageUrl(item.poster_path)}
                    alt={title}
                    fill
                    className="object-cover transition-transform group-hover:scale-105"
                  />
                  {item.vote_average > 0 && (
                    <div className="absolute top-2 right-2">
                      <Badge variant="secondary" className="text-xs flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-500 text-yellow-500" />
                        {item.vote_average.toFixed(1)}
                      </Badge>
                    </div>
                  )}
                </div>
                <CardContent className="p-3">
                  <h4 className="font-medium text-sm line-clamp-2 mb-1">
                    {title}
                  </h4>
                  <p className="text-xs text-muted-foreground">
                    {year}
                  </p>
                </CardContent>
              </Card>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
