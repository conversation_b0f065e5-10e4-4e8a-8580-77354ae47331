'use client'

import { useInfiniteQuery } from '@tanstack/react-query'
import { useEffect, useRef } from 'react'
import { TitleCard } from './title-card'
import { RecommendationsFeed } from './recommendations-feed'
import { tmdbClient } from '@/lib/tmdb'
import { Skeleton } from '@/components/ui/skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface FeedItem {
  id: string
  title: string
  year: number
  posterUrl: string
  contentType: 'movie' | 'tv' | 'anime'
  tmdbData?: any // Raw TMDB data for actions
}

export function MainFeed() {
  const loadMoreRef = useRef<HTMLDivElement>(null)

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteQuery({
    queryKey: ['feed', 'trending'],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        // Fetch both movies and TV shows
        const [moviesResponse, tvResponse] = await Promise.all([
          tmdbClient.getTrendingMovies(pageParam),
          tmdbClient.getTrendingTV(pageParam),
        ])

        console.log('Movies response:', moviesResponse.results.slice(0, 2))
        console.log('TV response:', tvResponse.results.slice(0, 2))

      // Combine and shuffle the results
      const movies: FeedItem[] = moviesResponse.results.map((movie) => ({
        id: `movie-${movie.id}`,
        title: movie.title,
        year: movie.release_date ? new Date(movie.release_date).getFullYear() : 0,
        posterUrl: tmdbClient.getImageUrl(movie.poster_path),
        contentType: 'movie' as const,
        tmdbData: {
          id: movie.id,
          media_type: 'movie' as const,
          title: movie.title,
          release_date: movie.release_date,
          poster_path: movie.poster_path,
          backdrop_path: movie.backdrop_path,
          overview: movie.overview,
        },
      }))

      const tvShows: FeedItem[] = tvResponse.results.map((show) => ({
        id: `tv-${show.id}`,
        title: show.name,
        year: show.first_air_date ? new Date(show.first_air_date).getFullYear() : 0,
        posterUrl: tmdbClient.getImageUrl(show.poster_path),
        contentType: 'tv' as const,
        tmdbData: {
          id: show.id,
          media_type: 'tv' as const,
          name: show.name,
          first_air_date: show.first_air_date,
          poster_path: show.poster_path,
          backdrop_path: show.backdrop_path,
          overview: show.overview,
        },
      }))

      const combined = [...movies, ...tvShows]
      // Shuffle array
      for (let i = combined.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[combined[i], combined[j]] = [combined[j], combined[i]]
      }

      console.log('Combined items:', combined.slice(0, 3))

      return {
        items: combined,
        nextPage: pageParam + 1,
        hasMore: pageParam < 5, // Limit to 5 pages for demo
      }
    } catch (error) {
      console.error('TMDb API Error:', error)
      throw error
    }
    },
    getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.nextPage : undefined),
    initialPageParam: 1,
  })

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage) {
          fetchNextPage()
        }
      },
      { threshold: 0.1 }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => observer.disconnect()
  }, [fetchNextPage, hasNextPage, isFetchingNextPage])

  const allItems = data?.pages.flatMap((page) => page.items) ?? []

  // Only show loading state for data loading, NOT auth loading
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {Array.from({ length: 20 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="aspect-[2/3] w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Oops! Something went wrong</h2>
          <p className="text-muted-foreground">
            Please check your TMDb API key in the environment variables.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">WatchIt</h1>
        <p className="text-muted-foreground">Discover your next favorite movie or show</p>
      </div>

      <Tabs defaultValue="feed" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="feed">Feed</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="browse">Browse</TabsTrigger>
          <TabsTrigger value="lists">Lists</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
        </TabsList>

        <TabsContent value="feed" className="mt-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {allItems.map((item) => (
              <TitleCard
                key={item.id}
                id={item.id}
                title={item.title}
                year={item.year}
                posterUrl={item.posterUrl}
                contentType={item.contentType}
                tmdbData={item.tmdbData}
                onLike={(liked) => console.log('Liked:', item.title, liked)}
                onRate={(rating) => console.log('Rated:', item.title, rating)}
                onWatchlist={(watchlisted) => console.log('Watchlisted:', item.title, watchlisted)}
                onRecommend={() => console.log('Recommended:', item.title)}
              />
            ))}
          </div>

          {/* Load more trigger */}
          <div ref={loadMoreRef} className="mt-8 flex justify-center">
            {isFetchingNextPage && (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 w-full">
                {Array.from({ length: 10 }).map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="aspect-[2/3] w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="mt-6">
          <RecommendationsFeed />
        </TabsContent>

        <TabsContent value="browse">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">Browse Coming Soon</h2>
            <p className="text-muted-foreground">Search and discover new content</p>
          </div>
        </TabsContent>

        <TabsContent value="lists">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">Lists Coming Soon</h2>
            <p className="text-muted-foreground">Manage your watchlists and custom lists</p>
          </div>
        </TabsContent>

        <TabsContent value="profile">
          <div className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">Profile Coming Soon</h2>
            <p className="text-muted-foreground">View and edit your profile</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
