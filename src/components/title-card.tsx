'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Toggle } from '@/components/ui/toggle'
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Heart, Star, Plus, Share2 } from 'lucide-react'
import { toast } from 'sonner'
import Image from 'next/image'
import Link from 'next/link'
import { TitleActions } from './title-actions'

interface TitleCardProps {
  id: string
  title: string
  year: number
  posterUrl: string
  contentType: 'movie' | 'tv' | 'anime'
  rating?: number
  isLiked?: boolean
  isWatchlisted?: boolean
  onLike?: (liked: boolean) => void
  onRate?: (rating: number) => void
  onWatchlist?: (watchlisted: boolean) => void
  onRecommend?: () => void
  // TMDB data for actions
  tmdbData?: {
    id: number
    media_type: 'movie' | 'tv'
    title?: string
    name?: string
    release_date?: string
    first_air_date?: string
    poster_path?: string
    backdrop_path?: string
    overview?: string
  }
}

export function TitleCard({
  id,
  title,
  year,
  posterUrl,
  contentType,
  rating = 0,
  isLiked = false,
  isWatchlisted = false,
  onLike,
  onRate,
  onWatchlist,
  onRecommend,
  tmdbData,
}: TitleCardProps) {
  const [showRatingDialog, setShowRatingDialog] = useState(false)
  const [currentRating, setCurrentRating] = useState(rating)

  const handleLike = () => {
    const newLiked = !isLiked
    onLike?.(newLiked)
    toast.success(newLiked ? 'Added to liked!' : 'Removed from liked')
  }

  const handleWatchlist = () => {
    const newWatchlisted = !isWatchlisted
    onWatchlist?.(newWatchlisted)
    toast.success(newWatchlisted ? 'Added to watchlist!' : 'Removed from watchlist')
  }

  const handleRating = (newRating: number) => {
    setCurrentRating(newRating)
    onRate?.(newRating)
    setShowRatingDialog(false)
    toast.success(`Rated ${newRating} star${newRating !== 1 ? 's' : ''}!`)
  }

  const handleRecommend = () => {
    onRecommend?.()
    toast.success('Recommendation shared!')
  }

  // Extract the numeric ID from the string ID (e.g., "movie-123" -> "123")
  const numericId = id.split('-')[1]
  const detailsUrl = `/details/${contentType}/${numericId}`

  return (
    <Card className="w-full max-w-sm overflow-hidden">
      <Link href={detailsUrl}>
        <div className="relative aspect-[2/3] overflow-hidden cursor-pointer">
          <Image
            src={posterUrl}
            alt={title}
            fill
            className="object-cover transition-transform hover:scale-105"
            onDoubleClick={(e) => {
              e.preventDefault()
              handleLike()
            }}
            onError={(e) => {
              console.error('Image failed to load:', posterUrl)
              e.currentTarget.src = 'https://via.placeholder.com/500x750/374151/f3f4f6?text=No+Image'
            }}
          />
          <div className="absolute top-2 right-2">
            <Badge variant="secondary" className="text-xs">
              {contentType.toUpperCase()}
            </Badge>
          </div>
        </div>
      </Link>
      
      <CardContent className="p-4">
        <div className="space-y-2">
          <div>
            <h3 className="font-semibold text-sm line-clamp-2">{title}</h3>
            <p className="text-xs text-muted-foreground">{year}</p>
          </div>
          
          <div className="flex items-center justify-between">
            {tmdbData ? (
              <TitleActions tmdbData={tmdbData} size="sm" />
            ) : (
              <div className="flex items-center space-x-1">
                <Toggle
                  pressed={isLiked}
                  onPressedChange={handleLike}
                  size="sm"
                  aria-label="Like"
                >
                  <Heart className={`h-4 w-4 ${isLiked ? 'fill-red-500 text-red-500' : ''}`} />
                </Toggle>

                <Dialog open={showRatingDialog} onOpenChange={setShowRatingDialog}>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Star className={`h-4 w-4 ${currentRating > 0 ? 'fill-yellow-500 text-yellow-500' : ''}`} />
                      {currentRating > 0 && <span className="ml-1 text-xs">{currentRating}</span>}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Rate {title}</DialogTitle>
                    </DialogHeader>
                    <div className="flex justify-center space-x-2 py-4">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Button
                          key={star}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRating(star)}
                          className="p-2"
                        >
                          <Star
                            className={`h-6 w-6 ${
                              star <= currentRating ? 'fill-yellow-500 text-yellow-500' : ''
                            }`}
                          />
                        </Button>
                      ))}
                    </div>
                  </DialogContent>
                </Dialog>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleWatchlist}
                  className={isWatchlisted ? 'text-blue-500' : ''}
                >
                  <Plus className="h-4 w-4" />
                </Button>

                <Button variant="ghost" size="sm" onClick={handleRecommend}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
