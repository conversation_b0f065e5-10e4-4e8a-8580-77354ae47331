'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { WatchlistPicker } from './watchlist-picker'
import { Heart, Plus, Star, Check, List, User, Share2 } from 'lucide-react'
import { useAuth } from '@/lib/auth-context'
import { watchlistService } from '@/lib/watchlist'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface TitleActionsProps {
  tmdbData: {
    id: number
    media_type: 'movie' | 'tv'
    title?: string
    name?: string
    release_date?: string
    first_air_date?: string
    poster_path?: string
    backdrop_path?: string
    overview?: string
  }
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function TitleActions({ tmdbData, className, size = 'md' }: TitleActionsProps) {
  const { user, loading: authLoading } = useAuth()
  const queryClient = useQueryClient()
  const [isProcessing, setIsProcessing] = useState(false)
  const [watchlistPickerOpen, setWatchlistPickerOpen] = useState(false)

  // Get title action data
  const { data: titleAction, isLoading } = useQuery({
    queryKey: ['title-action', tmdbData.id, tmdbData.media_type, user?.id],
    queryFn: async () => {
      if (!user) return null

      // First get or create the title
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) return null

      // Then get the user's action for this title
      return watchlistService.getTitleAction(title.id, user.id)
    },
    enabled: !!user,
  })

  // Get lists containing this title
  const { data: titleLists = [] } = useQuery({
    queryKey: ['title-lists', tmdbData.id, tmdbData.media_type, user?.id],
    queryFn: async () => {
      if (!user) return []

      // First get or create the title
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) return []

      // Then get lists containing this title
      return watchlistService.getListsContainingTitle(title.id, user.id)
    },
    enabled: !!user,
  })

  // Handle watchlist picker close
  const handleWatchlistPickerClose = () => {
    setWatchlistPickerOpen(false)
    // Refresh queries when picker closes
    queryClient.invalidateQueries({
      queryKey: ['title-action', tmdbData.id, tmdbData.media_type, user?.id]
    })
    queryClient.invalidateQueries({
      queryKey: ['title-lists', tmdbData.id, tmdbData.media_type, user?.id]
    })
  }

  // Like mutation
  const likeMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('Not authenticated')

      setIsProcessing(true)
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) throw new Error('Failed to create title')

      return watchlistService.toggleLike(title.id, user.id)
    },
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({
          queryKey: ['title-action', tmdbData.id, tmdbData.media_type, user?.id]
        })
      } else {
        toast.error('Something went wrong')
      }
      setIsProcessing(false)
    },
    onError: (error) => {
      toast.error('Something went wrong')
      setIsProcessing(false)
    }
  })

  // Recommendation mutation
  const recommendMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('Not authenticated')

      setIsProcessing(true)
      const title = await watchlistService.getOrCreateTitle(tmdbData)
      if (!title) throw new Error('Failed to create title')

      return watchlistService.toggleRecommendation(title.id, user.id)
    },
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({
          queryKey: ['title-action', tmdbData.id, tmdbData.media_type, user?.id]
        })
        const isRecommended = titleAction?.is_recommended || false
        toast.success(isRecommended ? 'Recommendation removed!' : 'Recommendation added!')
      } else {
        toast.error('Something went wrong')
      }
      setIsProcessing(false)
    },
    onError: (error) => {
      toast.error('Something went wrong')
      setIsProcessing(false)
    }
  })

  // If auth is still loading, show sign in prompt
  if (authLoading) {
    const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default'
    const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'

    return (
      <div className={cn('flex items-center gap-1', className)}>
        <Button variant="outline" size={buttonSize} disabled>
          <div className={cn(iconSize, 'animate-pulse bg-muted rounded')} />
          {size !== 'sm' && <span className="ml-1">Loading...</span>}
        </Button>
      </div>
    )
  }

  // If no user after auth loads, show sign in prompt
  if (!user) {
    const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default'
    const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'

    return (
      <div className={cn('flex items-center gap-1', className)}>
        <Button variant="outline" size={buttonSize} disabled>
          <User className={iconSize} />
          {size !== 'sm' && <span className="ml-1">Sign In</span>}
        </Button>
      </div>
    )
  }

  const isInLists = titleLists.length > 0
  const isLiked = titleAction?.liked || false
  const isRecommended = titleAction?.is_recommended || false
  const userRating = titleAction?.rating

  const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default'
  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'

  return (
    <>
      <div className={cn('flex items-center gap-1', className)}>
        {/* Watchlist Button */}
        <Button
          variant={isInLists ? 'default' : 'outline'}
          size={buttonSize}
          onClick={() => setWatchlistPickerOpen(true)}
          disabled={isLoading}
          className={cn(
            'transition-all',
            isInLists && 'bg-green-600 hover:bg-green-700'
          )}
        >
          {isInLists ? (
            <Check className={iconSize} />
          ) : (
            <List className={iconSize} />
          )}
          {size !== 'sm' && (
            <span className="ml-1">
              {isInLists
                ? `In ${titleLists.length} list${titleLists.length > 1 ? 's' : ''}`
                : 'Add to List'
              }
            </span>
          )}
        </Button>

      {/* Like Button */}
      <Button
        variant={isLiked ? 'default' : 'outline'}
        size={buttonSize}
        onClick={() => likeMutation.mutate()}
        disabled={isLoading || isProcessing || likeMutation.isPending}
        className={cn(
          'transition-all',
          isLiked && 'bg-red-600 hover:bg-red-700'
        )}
      >
        <Heart className={cn(iconSize, isLiked && 'fill-current')} />
        {size === 'lg' && (
          <span className="ml-1">
            {isLiked ? 'Liked' : 'Like'}
          </span>
        )}
      </Button>

      {/* Recommend Button */}
      <Button
        variant={isRecommended ? 'default' : 'outline'}
        size={buttonSize}
        onClick={() => recommendMutation.mutate()}
        disabled={isLoading || isProcessing || recommendMutation.isPending}
        className={cn(
          'transition-all',
          isRecommended && 'bg-blue-600 hover:bg-blue-700'
        )}
      >
        <Share2 className={iconSize} />
        {size === 'lg' && (
          <span className="ml-1">
            {isRecommended ? 'Recommended' : 'Recommend'}
          </span>
        )}
      </Button>

      {/* Rating Display */}
      {userRating && (
        <div className="flex items-center gap-1 px-2 py-1 bg-muted rounded text-sm">
          <Star className={cn(iconSize, 'fill-yellow-400 text-yellow-400')} />
          <span className="font-medium">{userRating}</span>
        </div>
      )}
      </div>

      {/* Watchlist Picker Modal */}
      <WatchlistPicker
        open={watchlistPickerOpen}
        onOpenChange={handleWatchlistPickerClose}
        tmdbData={tmdbData}
      />
    </>
  )
}
